import { RewardMechanics, <PERSON><PERSON>Roll, <PERSON>wardD<PERSON>inition, <PERSON>ward<PERSON><PERSON>ory, <PERSON>wardPool, GameEventData, RewardTrigger } from './types';

// Event emitter for reward roll updates
type RewardRollUpdateListener = (rewardRoll: RewardRoll) => void;
const rewardRollUpdateListeners: RewardRollUpdateListener[] = [];

const rewardRolls: RewardRoll[] = []
const rewardHistory: RewardHistory = {
     rewardPoolId: `pool-1`,
     rolls: [],
};


export const mockRewardPool : RewardPool = {
      id: `pool-1`,
      name: `Reward Pool for 1`,
      rewards: [
        {
          id: `reward-1`,
          name: "50% Off Coupon",
          description: "Get 50% off your next purchase",
          type: "coupon-code",
          image: {
            absoluteUrl: 'https://placehold.co/100x100/00ff00/ffffff?text=50OFF',
          },
        },
        {
          id: `reward-2`,
          name: "Free Shipping",
          description: "Free shipping on your next order",
          type: "claimable-url",
          image: {
            absoluteUrl: 'https://placehold.co/100x100/0000ff/ffffff?text=SHIP',
          },
        },
        {
          id: `reward-3`,
          name: "100% Off - Epic!",
          description: "Complete discount on selected items",
          type: "coupon-code",
          image: {
            absoluteUrl: 'https://placehold.co/100x100/ff0000/ffffff?text=100OFF',
          },
        },
        {
          id: `reward-4`,
          name: "A CAR!",
          description: "Complete discount on selected items",
          type: "coupon-code",
          image: {
            absoluteUrl: 'https://placehold.co/100x100/ff0000/ffffff?text=100OFF',
          },
        },
      ]
  }

export class RewardsManager {
  private gameWidgetId: string;
  private rewardMechanics: RewardMechanics;

  constructor(gameWidgetId: string, rewardMechanics: RewardMechanics) {
    this.gameWidgetId = gameWidgetId;
    this.rewardMechanics = rewardMechanics;

    //this is just to mock!
    rewardHistory.rewardPoolId = `pool-${gameWidgetId}`;
  }

  // Enhanced Event Handler
  public async handleGameEvent(
    triggerType: 'round_start' | 'round_finish' | 'game_finish',
    data: GameEventData
  ): Promise<RewardRoll | null> {
    const { roundId } = data;

    // Check new triggers system
    for (const trigger of this.rewardMechanics.triggers) {
      if (this.shouldTriggerReward(trigger, triggerType, data)) {
        const rewardRoll = await this.rollReward(roundId);
        return rewardRoll;
      }
    }

    return null;
  }

  // Screen Change Handler
  public async handleScreenChange(
    currentScreen: string,
    data: GameEventData
  ): Promise<RewardRoll | null> {
    const { roundId } = data;

    for (const trigger of this.rewardMechanics.triggers) {
      if (trigger.when === "on_screen_change" && trigger.screens.includes(currentScreen)) {
        const rewardRoll = await this.rollReward(roundId);
        return rewardRoll;
      }
    }

    return null;
  }

  // Trigger Evaluation Logic
  private shouldTriggerReward(
    trigger: RewardTrigger,
    legacyTriggerType: 'round_start' | 'round_finish' | 'game_finish',
    data: GameEventData
  ): boolean {
    switch (trigger.when) {
      case "round_start":
        return legacyTriggerType === "round_start";
      case "round_finish":
        return legacyTriggerType === "round_finish";
      case "game_finish":
        return legacyTriggerType === "game_finish";
      case "on_screen_change":
        return data.currentScreen ? trigger.screens.includes(data.currentScreen) : false;
      default:
        return false;
    }
  }



  // Reward Rolling
  private async rollReward(roundId: string): Promise<RewardRoll> {
    
    console.log("Rolling reard ", roundId)
    const rewardRoll: RewardRoll = {
      id: roundId + "_roll",
      roundId: roundId,
      gameWidgetId: this.gameWidgetId,
      timestamp: Date.now(),
    };
    console.log("Rolling reard rewardRoll:", rewardRoll)

    // Add to arrays and notify listeners immediately when rolling starts
    rewardRolls.push(rewardRoll);
    rewardHistory.rolls.push(rewardRoll);
    rewardRollUpdateListeners.forEach(listener => listener(rewardRoll));


    try {
      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 1000));

      // const hasWon = true
      const hasWon = true

      let reward: RewardDefinition | undefined;
      if (hasWon) {
        reward = mockRewardPool.rewards[0];
        // reward = null
      }

      rewardRoll.result = {
        hasWon,
        reward,
      };

    } catch (error) {
      // Error handling - could set result to indicate failure if needed
    }

    // Notify all listeners about the final reward roll update (completed or failed)
    rewardRollUpdateListeners.forEach(listener => listener(rewardRoll));

    return rewardRoll;
  }


  // State Access - will be replaced with backend API calls later
  public async getPickResultByRoundId(roundId: string): Promise<RewardRoll> {
    await new Promise(resolve => setTimeout(resolve, 1000));

    console.log("Rolls: ", rewardRolls)
    
    for (const roll of Object.values(rewardRolls)) {
      if (roll.roundId === roundId) {
        return roll;
      }
    }

    return null;
  }

  public async getRewardHistory(): Promise<RewardHistory> {
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1000));

    return rewardHistory;
  }
}

// Export functions to manage reward roll update listeners
export const addRewardRollUpdateListener = (listener: RewardRollUpdateListener): (() => void) => {
  rewardRollUpdateListeners.push(listener);

  // Return cleanup function
  return () => {
    const index = rewardRollUpdateListeners.indexOf(listener);
    if (index > -1) {
      rewardRollUpdateListeners.splice(index, 1);
    }
  };
};


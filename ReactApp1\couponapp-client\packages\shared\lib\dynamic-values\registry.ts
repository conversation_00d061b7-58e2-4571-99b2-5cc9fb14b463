import { DynamicValueSource } from './types'

// Global registry of value sources by component type
const valueSourceRegistry = new Map<string, DynamicValueSource[]>()

// Listeners for registry changes
const registryChangeListeners = new Set<() => void>()

/**
 * Register value sources for a specific component type
 * @param componentType - The component type identifier (e.g., 'quiz-game', 'timer-widget')
 * @param sources - Array of value source definitions
 */
export function registerValueSources(
  componentType: string,
  sources: DynamicValueSource[]
): void {
  if (valueSourceRegistry.has(componentType)) {
    console.warn(`Value sources for component type '${componentType}' are already registered. Overwriting.`)
  }

  console.log("Registering value sources for component type: ", componentType, sources)
  valueSourceRegistry.set(componentType, sources)

  // Notify all listeners that the registry has changed
  registryChangeListeners.forEach(listener => {
    try {
      listener()
    } catch (error) {
      console.error('Error in registry change listener:', error)
    }
  })
}

/**
 * Get registered value sources for a component type
 * @param componentType - The component type identifier
 * @returns Array of value source definitions, or empty array if not found
 */
export function getRegisteredSources(componentType: string): DynamicValueSource[] {
  return valueSourceRegistry.get(componentType) || []
}

/**
 * Get all registered component types
 * @returns Array of component type identifiers
 */
export function getAllRegisteredTypes(): string[] {
  return Array.from(valueSourceRegistry.keys())
}

/**
 * Check if a component type has registered value sources
 * @param componentType - The component type identifier
 * @returns True if the component type has registered sources
 */
export function hasRegisteredSources(componentType: string): boolean {
  return valueSourceRegistry.has(componentType)
}

/**
 * Clear all registered value sources (useful for testing)
 */
export function clearRegistry(): void {
  valueSourceRegistry.clear()
}

/**
 * Get the total number of registered component types
 */
export function getRegistrySize(): number {
  return valueSourceRegistry.size
}

/**
 * Subscribe to registry changes
 * @param listener - Function to call when registry changes
 * @returns Unsubscribe function
 */
export function onRegistryChange(listener: () => void): () => void {
  registryChangeListeners.add(listener)

  return () => {
    registryChangeListeners.delete(listener)
  }
}

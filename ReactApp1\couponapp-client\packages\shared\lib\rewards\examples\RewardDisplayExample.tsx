import React from 'react';
import { RewardRollProvider, useRewardRollDisplay, RewardRoll, RewardDefinition } from '../index';

// Example component that displays the current reward from reward roll
const RewardImage: React.FC = () => {
  const { rewardRoll } = useRewardRollDisplay();

  if (!rewardRoll || !rewardRoll.result?.hasWon || !rewardRoll.result.reward) {
    return <div className="text-gray-500">No reward to display</div>;
  }

  const reward = rewardRoll.result.reward;

  return (
    <div className="reward-display">
      {reward.image?.absoluteUrl && (
        <img
          src={reward.image.absoluteUrl}
          alt={reward.name || 'Reward'}
          className="w-24 h-24 object-cover rounded"
        />
      )}
      {reward.name && (
        <h3 className="text-lg font-semibold">{reward.name}</h3>
      )}
      {reward.description && (
        <p className="text-sm text-gray-600">{reward.description}</p>
      )}
      <div className="text-xs text-gray-400">
        Roll ID: {rewardRoll.id}
      </div>
    </div>
  );
};

// Example component that shows reward roll information
const RewardRollInfo: React.FC = () => {
  const { rewardRoll } = useRewardRollDisplay();

  if (!rewardRoll) {
    return <div className="text-gray-500">No reward roll provided</div>;
  }

  return (
    <div className="p-4 border rounded bg-gray-50">
      <h4 className="font-semibold">Reward Roll Information</h4>
      <div className="text-sm space-y-1">
        <div>ID: {rewardRoll.id}</div>
        <div>Round ID: {rewardRoll.roundId}</div>
        <div>Game Widget ID: {rewardRoll.gameWidgetId}</div>
        <div>Timestamp: {new Date(rewardRoll.timestamp).toLocaleString()}</div>
        {rewardRoll.result && (
          <div>
            <div>Has Won: {rewardRoll.result.hasWon ? 'Yes' : 'No'}</div>
            {rewardRoll.result.reward && (
              <div>Reward: {rewardRoll.result.reward.name}</div>
            )}
          </div>
        )}
      </div>
    </div>
  );
};

// Example usage of the RewardDisplayProvider
export const RewardDisplayExample: React.FC = () => {
  // Sample reward roll for demonstration
  const sampleRewardRoll: RewardRoll = {
    id: 'roll-123',
    roundId: 'round-456',
    gameWidgetId: 'game-789',
    timestamp: Date.now(),
    result: {
      hasWon: true,
      reward: {
        id: 'reward-1',
        name: '50% Off Coupon',
        description: 'Get 50% off your next purchase',
        type: 'coupon-code',
        image: {
          absoluteUrl: 'https://placehold.co/100x100/00ff00/ffffff?text=50OFF'
        }
      }
    }
  };

  return (
    <RewardRollProvider rewardRoll={sampleRewardRoll}>
      <div className="p-4 space-y-4">
        <h2 className="text-xl font-bold">Reward Roll Example</h2>
        <RewardRollInfo />
        <RewardImage />
      </div>
    </RewardRollProvider>
  );
};

export default RewardDisplayExample;

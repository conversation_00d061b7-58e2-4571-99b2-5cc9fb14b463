// Core dynamic values system
export { DynamicValuesProvider } from './provider'
export { useDynamicValues } from './hooks/useDynamicValues'
export { useLiveValue, useLiveValues } from './hooks/useLiveValue'
export { useDynamicExpression } from './hooks/useDynamicExpression'

// Expression parser
export {
  parseExpression,
  renderExpression,
  renderExpressionWithVariables,
  extractVariablePaths,
  extractExpressionVariables,
  extractExpressionVariablesFromPath,
  resolveExpressionVariablesInPath,
  validateExpression,
  hasDynamicContent,
  replaceVariablePath
} from './expression-parser'

// Expression variables
export { 
  ExpressionVariableContextProvider, 
  useExpressionVariables 
} from './expression-variables'

// Registry
export { registerValueSources, getRegisteredSources, onRegistryChange } from './registry'

// Types
export type { 
  DynamicValueSource, 
  ResolvedValueSource, 
  ValueSourceCatalog,
  DynamicValuesContextValue 
} from './types'

export type {
  ExpressionVariableContextValue,
  ExpressionVariableProviderProps
} from './expression-variables'

// Components
export { ValueSourcePicker } from './components/ValueSourcePicker'
